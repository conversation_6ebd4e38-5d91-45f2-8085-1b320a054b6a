import React, { useState, useCallback, useEffect } from 'react';
import { UserProfile, AnimeBadge } from './types';
import { analyzeProfile, AnalysisResult } from './services/geminiService';
import { SearchForm } from './components/SearchForm';
import { UserCard } from './components/UserCard';
import { BadgeDisplay } from './components/BadgeDisplay';
import { Loader } from './components/Loader';
import { ErrorAlert } from './components/ErrorAlert';
import { ShinobiScrollIcon } from './components/icons';

const App: React.FC = () => {
  const [usernameInput, setUsernameInput] = useState<string>('torvalds');
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [badgeInfo, setBadgeInfo] = useState<AnimeBadge | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Convert analysis result to legacy format for existing components
  const convertAnalysisResult = (result: AnalysisResult) => {
    const profile: UserProfile = {
      user: {
        login: result.username,
        name: result.profile.name,
        bio: result.profile.bio,
        avatar_url: '', // Will be set from GitHub data
        followers: result.profile.followers,
        following: 0, // Not provided in new format
        public_repos: result.profile.publicRepos,
        created_at: '', // Not provided in new format
        html_url: `https://github.com/${result.username}`,
        location: null,
        blog: null,
        company: null
      },
      repos: [] // Repos not needed for display in current components
    };

    const badge: AnimeBadge = {
      characterName: result.character.name,
      anime: result.character.anime as 'Naruto' | 'Demon Slayer',
      reason: result.character.reason,
      badgeColor: result.character.color
    };

    return { profile, badge };
  };

  const handleSearch = useCallback(async () => {
    if (!usernameInput) {
        setError('Please enter a GitHub username.');
        return;
    }

    setIsLoading(true);
    setError(null);
    setAnalysisResult(null);
    setUserProfile(null);
    setBadgeInfo(null);
    setAvatarUrl(null);

    try {
      // Use new backend API for complete analysis
      const result = await analyzeProfile(usernameInput);
      setAnalysisResult(result);

      // Convert to legacy format for existing components
      const { profile, badge } = convertAnalysisResult(result);
      setUserProfile(profile);
      setBadgeInfo(badge);
      setAvatarUrl(result.avatar);

      // Debug logging
      console.log('Analysis result:', result);
      console.log('Avatar URL length:', result.avatar?.length);
      console.log('Avatar URL preview:', result.avatar?.substring(0, 100) + '...');

    } catch (err: any) {
      setError(err.message || 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  }, [usernameInput]);

  // Auto-search on component mount for demo
  useEffect(() => {
    if (usernameInput === 'torvalds') {
      // Small delay to show the interface first
      setTimeout(() => {
        handleSearch();
      }, 1000);
    }
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 to-slate-800 text-white font-sans p-4 sm:p-6 md:p-8">
      <div className="max-w-4xl mx-auto">
        <header className="text-center mb-8">
          <div className="flex items-center justify-center gap-4 mb-2">
            <ShinobiScrollIcon className="h-12 w-12 text-teal-400" />
            <h1 className="text-4xl sm:text-5xl font-bold tracking-tighter text-transparent bg-clip-text bg-gradient-to-r from-teal-300 to-sky-500">
              RepoSensei
            </h1>
          </div>
          <p className="text-slate-400 max-w-2xl mx-auto">
            Discover your inner coding ninja through AI-powered GitHub profile analysis. Get matched with anime characters from Naruto and Demon Slayer!
          </p>
        </header>

        <main>
          <SearchForm
            value={usernameInput}
            onChange={(e) => setUsernameInput(e.target.value)}
            onSubmit={handleSearch}
            isLoading={isLoading}
            placeholder="e.g., torvalds, gaearon"
          />

          {error && <ErrorAlert message={error} />}

          <div className="mt-8">
            {isLoading && <Loader />}

            {userProfile && badgeInfo && !isLoading && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 animate-fade-in">
                <UserCard profile={userProfile} />
                <BadgeDisplay
                    badge={badgeInfo}
                    avatarUrl={avatarUrl}
                    isLoadingAvatar={false}
                />
              </div>
            )}

            {!isLoading && !userProfile && !error && (
                 <div className="text-center py-16 px-4 bg-gray-800/50 rounded-lg border border-slate-700">
                    <p className="text-slate-400">Enter a GitHub username to discover your inner coding ninja!</p>
                </div>
            )}
          </div>
        </main>
        
        <footer className="text-center mt-12 text-slate-500 text-sm">
            <p>Powered by free open-source AI models. Character assignments and avatars are generated using Llama 3.1 and Stable Diffusion XL.</p>
        </footer>
      </div>
    </div>
  );
};

export default App;
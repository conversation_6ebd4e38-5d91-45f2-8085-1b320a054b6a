@echo off
echo Starting Git-Sensei Development Environment...
echo.

REM Kill any existing processes on ports 3001 and 5173
echo Cleaning up existing processes...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3001') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5173') do taskkill /f /pid %%a >nul 2>&1

echo.
echo Starting Backend Server (Port 3001)...
start "Backend" cmd /k "cd backend && npm run dev"

echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

echo.
echo Starting Frontend Server (Port 5173)...
start "Frontend" cmd /k "npm run dev"

echo.
echo ========================================
echo Git-Sensei Development Environment
echo ========================================
echo Frontend: http://localhost:5173
echo Backend:  http://localhost:3001
echo Health:   http://localhost:3001/health
echo ========================================
echo.
echo Press any key to stop all services...
pause >nul

echo.
echo Stopping services...
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3001') do taskkill /f /pid %%a >nul 2>&1
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :5173') do taskkill /f /pid %%a >nul 2>&1

echo Services stopped.
pause

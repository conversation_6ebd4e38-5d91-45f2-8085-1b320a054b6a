<!DOCTYPE html>
<html>
<head>
    <title>Avatar Test</title>
</head>
<body>
    <h1>Avatar Test</h1>
    <div id="result"></div>
    
    <script>
        async function testAvatar() {
            try {
                const response = await fetch('http://localhost:3001/api/v1/analysis/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username: 'test' })
                });
                
                const data = await response.json();
                console.log('Response:', data);
                
                if (data.success && data.data.avatar) {
                    const img = document.createElement('img');
                    img.src = data.data.avatar;
                    img.style.width = '200px';
                    img.style.height = '200px';
                    img.onload = () => console.log('Image loaded successfully');
                    img.onerror = (e) => console.error('Image failed to load:', e);
                    
                    document.getElementById('result').appendChild(img);
                    
                    // Show avatar info
                    const info = document.createElement('div');
                    info.innerHTML = `
                        <p>Avatar length: ${data.data.avatar.length}</p>
                        <p>Avatar preview: ${data.data.avatar.substring(0, 100)}...</p>
                        <p>Character: ${data.data.character.name}</p>
                        <p>Bio: ${data.data.profile.bio || 'No bio provided'}</p>
                    `;
                    document.getElementById('result').appendChild(info);
                } else {
                    document.getElementById('result').innerHTML = 'Failed to get avatar: ' + JSON.stringify(data);
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
        
        testAvatar();
    </script>
</body>
</html>

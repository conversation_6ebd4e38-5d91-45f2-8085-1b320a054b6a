<!DOCTYPE html>
<html>
<head>
    <title>Avatar Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 20px 0; padding: 20px; border: 1px solid #ccc; border-radius: 8px; }
        .avatar-img { border: 2px solid #333; border-radius: 8px; margin: 10px 0; }
        .loading { color: #666; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Git-Sensei Avatar Test</h1>
    <div id="result">
        <div class="loading">Testing avatar generation...</div>
    </div>

    <script>
        async function testAvatar() {
            const resultDiv = document.getElementById('result');

            try {
                resultDiv.innerHTML = '<div class="loading">Calling API...</div>';

                const response = await fetch('http://localhost:3001/api/v1/analysis/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username: 'sindresorhus' })
                });

                const data = await response.json();
                console.log('API Response:', data);

                if (data.success && data.data.avatar) {
                    const testResult = document.createElement('div');
                    testResult.className = 'test-result';

                    // Create image element
                    const img = document.createElement('img');
                    img.src = data.data.avatar;
                    img.className = 'avatar-img';
                    img.style.width = '200px';
                    img.style.height = '200px';
                    img.style.objectFit = 'cover';

                    let imageStatus = 'Loading...';

                    img.onload = () => {
                        console.log('✅ Image loaded successfully');
                        imageStatus = '✅ Image loaded successfully';
                        updateStatus();
                    };

                    img.onerror = (e) => {
                        console.error('❌ Image failed to load:', e);
                        imageStatus = '❌ Image failed to load';
                        updateStatus();
                    };

                    function updateStatus() {
                        const statusDiv = testResult.querySelector('.status');
                        if (statusDiv) {
                            statusDiv.innerHTML = imageStatus;
                            statusDiv.className = imageStatus.includes('✅') ? 'status success' : 'status error';
                        }
                    }

                    testResult.innerHTML = `
                        <h3>Analysis Result for: ${data.data.username}</h3>
                        <div class="status loading">${imageStatus}</div>
                        <div style="margin: 10px 0;"></div>
                        <p><strong>Character:</strong> ${data.data.character.name} (${data.data.character.anime})</p>
                        <p><strong>Bio:</strong> ${data.data.profile.bio || 'No bio provided'}</p>
                        <p><strong>Avatar Data:</strong> ${data.data.avatar.length} characters</p>
                        <p><strong>Avatar Format:</strong> ${data.data.avatar.substring(0, 30)}...</p>
                        <p><strong>Reason:</strong> ${data.data.character.reason}</p>
                    `;

                    testResult.insertBefore(img, testResult.children[2]);
                    resultDiv.innerHTML = '';
                    resultDiv.appendChild(testResult);

                    updateStatus();

                } else {
                    resultDiv.innerHTML = `<div class="test-result error">
                        <h3>❌ API Error</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>`;
                }
            } catch (error) {
                console.error('Network Error:', error);
                resultDiv.innerHTML = `<div class="test-result error">
                    <h3>❌ Network Error</h3>
                    <p>${error.message}</p>
                </div>`;
            }
        }

        // Start test
        testAvatar();
    </script>
</body>
</html>

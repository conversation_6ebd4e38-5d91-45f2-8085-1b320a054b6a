import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from multiple possible locations
dotenv.config({ path: path.join(__dirname, '../.env') });
dotenv.config({ path: path.join(process.cwd(), '.env') });
dotenv.config(); // Also try default locations

import App from './app.js';
import { logger, logError } from './utils/logger.js';
import { testDatabaseConnection, closeDatabaseConnection } from './config/database.js';
import { redis } from './config/redis.js';

class Server {
  private app: App;
  private server: any;
  private port: number;
  private host: string;

  constructor() {
    this.app = new App();
    this.port = parseInt(process.env.PORT || '3001', 10);
    this.host = process.env.HOST || 'localhost';
  }

  /**
   * Initialize all services and start the server
   */
  public async start(): Promise<void> {
    try {
      logger.info('Starting RepoSensei Backend Server...');

      // Validate required environment variables
      await this.validateEnvironment();

      // Initialize services
      await this.initializeServices();

      // Start HTTP server
      await this.startHttpServer();

      // Setup graceful shutdown
      this.setupGracefulShutdown();

      logger.info('🚀 RepoSensei Backend Server started successfully', {
        port: this.port,
        host: this.host,
        environment: process.env.NODE_ENV || 'development',
        nodeVersion: process.version,
        pid: process.pid
      });

    } catch (error) {
      logError('Failed to start server', error as Error);
      process.exit(1);
    }
  }

  /**
   * Validate required environment variables
   */
  private async validateEnvironment(): Promise<void> {
    const requiredEnvVars = [
      'DATABASE_URL',
      'JWT_SECRET',
      'REDIS_URL'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
    }

    // Validate JWT secret strength
    const jwtSecret = process.env.JWT_SECRET!;
    if (jwtSecret.length < 32) {
      logger.warn('JWT_SECRET is shorter than recommended (32 characters)');
    }

    // Validate database URL format
    const databaseUrl = process.env.DATABASE_URL!;
    if (!databaseUrl.startsWith('postgresql://') && !databaseUrl.startsWith('postgres://')) {
      throw new Error('DATABASE_URL must be a valid PostgreSQL connection string');
    }

    logger.info('Environment validation completed');
  }

  /**
   * Initialize all external services
   */
  private async initializeServices(): Promise<void> {
    logger.info('Initializing services...');

    // Test database connection
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected && process.env.NODE_ENV === 'production') {
      throw new Error('Failed to connect to database');
    }

    // Test Redis connection
    const redisHealthy = await redis.healthCheck();
    if (redisHealthy.status !== 'healthy') {
      logger.warn('Redis connection failed, some features may be limited', {
        error: redisHealthy.error
      });
    }

    // Validate GitHub token if provided
    if (process.env.GITHUB_TOKEN) {
      logger.info('GitHub token configured');
    } else {
      logger.warn('GitHub token not configured - API rate limits will be restricted');
    }

    // Check AI services configuration
    this.checkAIServicesConfig();

    logger.info('Services initialization completed');
  }

  /**
   * Check AI services configuration
   */
  private checkAIServicesConfig(): void {
    const aiServices = [];

    // Check Ollama configuration
    if (process.env.OLLAMA_HOST) {
      aiServices.push('Ollama (Local LLM)');
    }

    // Check Hugging Face configuration
    if (process.env.HUGGINGFACE_API_KEY) {
      aiServices.push('Hugging Face');
    }

    // Check Stable Diffusion configuration
    if (process.env.STABLE_DIFFUSION_API_URL) {
      aiServices.push('Stable Diffusion (Local)');
    }

    // Check Unsplash configuration
    if (process.env.UNSPLASH_ACCESS_KEY) {
      aiServices.push('Unsplash (Fallback Images)');
    }

    if (aiServices.length === 0) {
      logger.warn('No AI services configured - application will use fallback responses');
    } else {
      logger.info('AI services configured', { services: aiServices });
    }
  }

  /**
   * Start the HTTP server
   */
  private async startHttpServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = this.app.getApp().listen(this.port, this.host, () => {
        resolve();
      });

      this.server.on('error', (error: any) => {
        if (error.code === 'EADDRINUSE') {
          reject(new Error(`Port ${this.port} is already in use`));
        } else if (error.code === 'EACCES') {
          reject(new Error(`Permission denied to bind to port ${this.port}`));
        } else {
          reject(error);
        }
      });

      // Set server timeout
      this.server.timeout = 30000; // 30 seconds

      // Handle server timeout
      this.server.on('timeout', (socket: any) => {
        logger.warn('Server timeout occurred', {
          remoteAddress: socket.remoteAddress,
          remotePort: socket.remotePort
        });
        socket.destroy();
      });
    });
  }

  /**
   * Setup graceful shutdown handlers
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`);

      // Stop accepting new connections
      if (this.server) {
        this.server.close(async () => {
          logger.info('HTTP server closed');

          try {
            // Close database connections
            await closeDatabaseConnection();
            logger.info('Database connections closed');

            // Close Redis connection
            await redis.disconnect();
            logger.info('Redis connection closed');

            logger.info('Graceful shutdown completed');
            process.exit(0);
          } catch (error) {
            logError('Error during graceful shutdown', error as Error);
            process.exit(1);
          }
        });

        // Force close after timeout
        setTimeout(() => {
          logger.error('Graceful shutdown timeout, forcing exit');
          process.exit(1);
        }, 10000); // 10 seconds timeout
      } else {
        process.exit(0);
      }
    };

    // Handle different shutdown signals
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // Nodemon restart

    // Handle uncaught exceptions
    process.on('uncaughtException', (error: Error) => {
      logError('Uncaught Exception - Server will exit', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
      logError('Unhandled Promise Rejection - Server will exit', new Error(reason));
      process.exit(1);
    });
  }

  /**
   * Get server instance for testing
   */
  public getServer(): any {
    return this.server;
  }

  /**
   * Stop the server (for testing)
   */
  public async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          logger.info('Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const server = new Server();
  server.start().catch((error) => {
    logError('Failed to start server', error);
    process.exit(1);
  });
}

export default Server;

# 🚀 RepoSensei - Complete Setup Guide

## 🔧 **FIXED ISSUES**

✅ **Frontend npm dependency error**: Updated package.json with compatible versions  
✅ **Frontend Vite module error**: Ensured Vite is installed locally  
✅ **Backend JWT_SECRET error**: Added proper JWT_SECRET in backend/.env  
✅ **ESLint configuration**: Simplified to avoid version conflicts  
✅ **Environment loading**: Enhanced backend environment variable loading  

## 📋 **PREREQUISITES**

- **Node.js** 18+ installed
- **npm** (comes with Node.js)
- **Git** (for cloning)

## 🚀 **QUICK START**

### **Option 1: Automated Installation (Recommended)**

```bash
# Windows
install.bat

# Linux/Mac
chmod +x install.sh
./install.sh
```

### **Option 2: Manual Installation**

```bash
# 1. Clean previous installations
rm -rf node_modules package-lock.json
rm -rf backend/node_modules backend/package-lock.json

# 2. Install frontend dependencies
npm install --legacy-peer-deps

# 3. Install backend dependencies
cd backend
npm install
cd ..
```

## 🎯 **STARTING THE APPLICATION**

### **Option 1: Automated Startup (Recommended)**

```bash
# Windows
start-dev.bat

# Linux/Mac
chmod +x start-dev.sh
./start-dev.sh
```

### **Option 2: Manual Startup**

```bash
# Terminal 1: Backend
cd backend
npm run dev

# Terminal 2: Frontend (new terminal)
npm run dev
```

## 🌐 **ACCESS THE APPLICATION**

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3001
- **Health Check**: http://localhost:3001/health

## 🔍 **TESTING THE APPLICATION**

1. **Open** http://localhost:5173 in your browser
2. **Enter** a GitHub username (e.g., "torvalds", "octocat")
3. **Click** "Analyze Profile"
4. **Wait** for the AI analysis to complete
5. **View** your assigned anime character!

## 🛠️ **CONFIGURATION**

### **Backend Environment Variables** (`backend/.env`)

```env
# Already configured for development
JWT_SECRET=reposensei-development-jwt-secret-key-very-long-and-secure-for-development-only-change-in-production
DATABASE_URL=postgresql://postgres:password@localhost:5432/reposensei
REDIS_URL=redis://localhost:6379
```

### **Frontend Environment Variables** (`.env`)

```env
# Already configured for development
VITE_API_URL=http://localhost:3001/api/v1
VITE_APP_NAME=RepoSensei
```

## 🔧 **TROUBLESHOOTING**

### **Common Issues & Solutions**

#### **1. npm install fails**
```bash
# Clear cache and try again
npm cache clean --force
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

#### **2. Vite not found**
```bash
# Ensure Vite is installed locally
npm install vite@^5.0.8 --save-dev
```

#### **3. Backend JWT_SECRET error**
```bash
# Check backend/.env file exists and has JWT_SECRET
cat backend/.env | grep JWT_SECRET
```

#### **4. Port already in use**
```bash
# Kill processes on ports 3001 and 5173
npx kill-port 3001 5173
```

#### **5. TypeScript errors**
```bash
# Run type check
npm run type-check
```

### **Reset Everything**

```bash
# Complete reset
rm -rf node_modules package-lock.json
rm -rf backend/node_modules backend/package-lock.json
npm cache clean --force
npm install --legacy-peer-deps
cd backend && npm install && cd ..
```

## 📊 **DEVELOPMENT FEATURES**

- ✅ **No External Dependencies Required**: Works without database, Redis, or AI services
- ✅ **Fallback Responses**: Uses mock data when external services unavailable
- ✅ **Hot Reload**: Both frontend and backend support hot reloading
- ✅ **Error Handling**: Comprehensive error handling and logging
- ✅ **CORS Configured**: Frontend can communicate with backend
- ✅ **Environment Variables**: Properly configured for development

## 🎨 **FEATURES TO TEST**

1. **GitHub Profile Analysis**: Enter any valid GitHub username
2. **Character Assignment**: Get matched with Naruto/Demon Slayer characters
3. **Error Handling**: Try invalid usernames to see error messages
4. **Responsive Design**: Test on different screen sizes
5. **API Health**: Visit http://localhost:3001/health

## 📝 **NEXT STEPS**

1. **Add GitHub Token**: For higher API rate limits
2. **Set up Database**: For persistent storage
3. **Configure AI Services**: For real AI analysis
4. **Deploy**: Use Docker or cloud platforms

## 🆘 **SUPPORT**

If you encounter any issues:

1. **Check the logs** in both terminal windows
2. **Verify ports** 3001 and 5173 are available
3. **Ensure Node.js** 18+ is installed
4. **Try the reset commands** above
5. **Check network connectivity** for GitHub API access

---

**🎉 You should now have RepoSensei running successfully!**

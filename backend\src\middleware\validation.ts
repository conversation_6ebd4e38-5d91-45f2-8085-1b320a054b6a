import { Request, Response, NextFunction } from 'express';
import { z, ZodError, ZodSchema } from 'zod';
import { logger, logError } from '../utils/logger.js';

// Common validation schemas
export const commonSchemas = {
  // GitHub username validation
  githubUsername: z.string()
    .min(1, 'Username is required')
    .max(39, 'Username must be 39 characters or less')
    .regex(/^[a-zA-Z0-9]([a-zA-Z0-9-])*[a-zA-Z0-9]$/, 'Invalid GitHub username format'),

  // Email validation
  email: z.string()
    .email('Invalid email format')
    .max(254, 'Email too long'),

  // Password validation
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password too long')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),

  // Pagination
  pagination: z.object({
    page: z.coerce.number().int().min(1).default(1),
    limit: z.coerce.number().int().min(1).max(100).default(10)
  }),

  // UUID validation
  uuid: z.string().uuid('Invalid UUID format'),

  // Color hex code validation
  hexColor: z.string().regex(/^#[0-9A-Fa-f]{6}$/, 'Invalid hex color format'),

  // URL validation
  url: z.string().url('Invalid URL format').max(2048, 'URL too long')
};

// Request validation schemas
export const validationSchemas = {
  // Analysis requests
  analyzeProfile: z.object({
    body: z.object({
      username: commonSchemas.githubUsername
    })
  }),

  batchAnalyze: z.object({
    body: z.object({
      usernames: z.array(commonSchemas.githubUsername)
        .min(1, 'At least one username required')
        .max(10, 'Maximum 10 usernames allowed')
    })
  }),

  // User authentication
  register: z.object({
    body: z.object({
      email: commonSchemas.email,
      password: commonSchemas.password,
      name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
      githubUsername: commonSchemas.githubUsername.optional()
    })
  }),

  login: z.object({
    body: z.object({
      email: commonSchemas.email,
      password: z.string().min(1, 'Password is required')
    })
  }),

  // Profile updates
  updateProfile: z.object({
    body: z.object({
      name: z.string().min(1).max(100).optional(),
      bio: z.string().max(500).optional(),
      githubUsername: commonSchemas.githubUsername.optional(),
      preferences: z.object({
        theme: z.enum(['light', 'dark', 'auto']).optional(),
        notifications: z.boolean().optional(),
        publicProfile: z.boolean().optional()
      }).optional()
    })
  }),

  // Search and filtering
  searchUsers: z.object({
    query: z.object({
      q: z.string().min(1, 'Search query required').max(100),
      limit: z.coerce.number().int().min(1).max(50).default(10)
    })
  }),

  // Admin operations
  adminUserUpdate: z.object({
    body: z.object({
      role: z.enum(['user', 'admin', 'moderator']).optional(),
      status: z.enum(['active', 'suspended', 'banned']).optional(),
      permissions: z.array(z.string()).optional()
    })
  }),

  // Feedback and reporting
  submitFeedback: z.object({
    body: z.object({
      type: z.enum(['bug', 'feature', 'improvement', 'other']),
      title: z.string().min(1, 'Title is required').max(200),
      description: z.string().min(10, 'Description must be at least 10 characters').max(2000),
      priority: z.enum(['low', 'medium', 'high']).default('medium'),
      metadata: z.record(z.any()).optional()
    })
  }),

  // Character and analysis management
  updateCharacterData: z.object({
    body: z.object({
      characterName: z.string().min(1).max(100),
      anime: z.enum(['Naruto', 'Demon Slayer']),
      description: z.string().max(1000).optional(),
      traits: z.array(z.string()).optional(),
      color: commonSchemas.hexColor,
      imageUrl: commonSchemas.url.optional()
    })
  })
};

class ValidationMiddleware {
  /**
   * Generic validation middleware factory
   */
  public validate = (schema: ZodSchema) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        // Validate the entire request object (body, query, params)
        const validatedData = schema.parse({
          body: req.body,
          query: req.query,
          params: req.params
        });

        // Replace request data with validated data
        req.body = validatedData.body || req.body;
        req.query = validatedData.query || req.query;
        req.params = validatedData.params || req.params;

        next();
      } catch (error) {
        this.handleValidationError(error, req, res);
      }
    };
  };

  /**
   * Validate request body only
   */
  public validateBody = (schema: ZodSchema) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        req.body = schema.parse(req.body);
        next();
      } catch (error) {
        this.handleValidationError(error, req, res);
      }
    };
  };

  /**
   * Validate query parameters only
   */
  public validateQuery = (schema: ZodSchema) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        req.query = schema.parse(req.query);
        next();
      } catch (error) {
        this.handleValidationError(error, req, res);
      }
    };
  };

  /**
   * Validate URL parameters only
   */
  public validateParams = (schema: ZodSchema) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      try {
        req.params = schema.parse(req.params);
        next();
      } catch (error) {
        this.handleValidationError(error, req, res);
      }
    };
  };

  /**
   * Handle validation errors
   */
  private handleValidationError(error: unknown, req: Request, res: Response): void {
    if (error instanceof ZodError) {
      const formattedErrors = error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
        received: err.received
      }));

      logError('Validation error', error, {
        endpoint: req.path,
        method: req.method,
        errors: formattedErrors,
        body: req.body,
        query: req.query,
        params: req.params
      });

      res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: formattedErrors
      });
      return;
    }

    // Handle unexpected validation errors
    logError('Unexpected validation error', error as Error, {
      endpoint: req.path,
      method: req.method
    });

    res.status(500).json({
      success: false,
      error: 'Validation service error'
    });
  }

  /**
   * Sanitize input data
   */
  public sanitize = (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Sanitize strings in body
      if (req.body && typeof req.body === 'object') {
        req.body = this.sanitizeObject(req.body);
      }

      // Sanitize query parameters
      if (req.query && typeof req.query === 'object') {
        req.query = this.sanitizeObject(req.query);
      }

      next();
    } catch (error) {
      logError('Sanitization error', error as Error, {
        endpoint: req.path,
        method: req.method
      });
      next();
    }
  };

  /**
   * Recursively sanitize object properties
   */
  private sanitizeObject(obj: any): any {
    if (typeof obj !== 'object' || obj === null) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }

    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string') {
        // Basic XSS prevention
        sanitized[key] = value
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '')
          .trim();
      } else {
        sanitized[key] = this.sanitizeObject(value);
      }
    }

    return sanitized;
  }

  /**
   * Validate GitHub username format
   */
  public validateGitHubUsername = (req: Request, res: Response, next: NextFunction): void => {
    const username = req.body.username || req.params.username || req.query.username;
    
    if (!username) {
      res.status(400).json({
        success: false,
        error: 'GitHub username is required'
      });
      return;
    }

    try {
      commonSchemas.githubUsername.parse(username);
      next();
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Invalid GitHub username format'
      });
    }
  };

  /**
   * Validate pagination parameters
   */
  public validatePagination = (req: Request, res: Response, next: NextFunction): void => {
    try {
      const validated = commonSchemas.pagination.parse(req.query);
      req.query = { ...req.query, ...validated };
      next();
    } catch (error) {
      res.status(400).json({
        success: false,
        error: 'Invalid pagination parameters'
      });
    }
  };

  /**
   * Validate file upload
   */
  public validateFileUpload = (allowedTypes: string[], maxSize: number) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (!req.file) {
        res.status(400).json({
          success: false,
          error: 'File is required'
        });
        return;
      }

      // Check file type
      if (!allowedTypes.includes(req.file.mimetype)) {
        res.status(400).json({
          success: false,
          error: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`
        });
        return;
      }

      // Check file size
      if (req.file.size > maxSize) {
        res.status(400).json({
          success: false,
          error: `File too large. Maximum size: ${maxSize} bytes`
        });
        return;
      }

      next();
    };
  };
}

// Export singleton instance
export const validationMiddleware = new ValidationMiddleware();
export default validationMiddleware;

import { createClient } from '@supabase/supabase-js';
import { Pool } from 'pg';
import { logger } from '../utils/logger.js';

// Supabase Configuration (Optional)
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Only require Supabase config if explicitly using Supabase features
const useSupabase = supabaseUrl && supabaseAnonKey &&
  !supabaseUrl.includes('your-project') &&
  !supabaseAnonKey.includes('your-anon-key');

// Public Supabase client (for auth and public operations) - Optional
export const supabase = useSupabase ? createClient(supabaseUrl!, supabaseAnonKey!, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
}) : null;

// Admin Supabase client (for admin operations) - Optional
export const supabaseAdmin = useSupabase && supabaseServiceKey ? createClient(supabaseUrl!, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
}) : null;

// PostgreSQL Pool Configuration (Optional for development)
const databaseUrl = process.env.DATABASE_URL;

// Use in-memory fallback for development
const useDatabase = process.env.NODE_ENV === 'production' && databaseUrl;

if (!useDatabase) {
  logger.warn('No database configured. Using in-memory storage for development.');
}

export const pool = useDatabase ? new Pool({
  connectionString: databaseUrl,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
}) : null;

// Database connection test
export const testDatabaseConnection = async (): Promise<boolean> => {
  if (!useDatabase || !pool) {
    logger.info('Database not configured - using in-memory storage');
    return true;
  }

  try {
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    logger.info('Database connection successful');
    return true;
  } catch (error) {
    logger.error('Database connection failed:', error);
    return false;
  }
};

// Graceful shutdown
export const closeDatabaseConnection = async (): Promise<void> => {
  try {
    await pool.end();
    logger.info('Database connection closed');
  } catch (error) {
    logger.error('Error closing database connection:', error);
  }
};

// Database query helper with error handling
export const query = async (text: string, params?: any[]): Promise<any> => {
  if (!useDatabase || !pool) {
    logger.debug('Database query skipped - using in-memory storage');
    return { rows: [], rowCount: 0 };
  }

  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    logger.debug('Executed query', { text, duration, rows: result.rowCount });
    return result;
  } catch (error) {
    logger.error('Database query error:', { text, error });
    throw error;
  }
};

// Transaction helper
export const transaction = async (callback: (client: any) => Promise<any>): Promise<any> => {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
};

// Health check for database
export const healthCheck = async (): Promise<{ status: string; latency?: number; error?: string }> => {
  const start = Date.now();
  try {
    await pool.query('SELECT 1');
    const latency = Date.now() - start;
    return { status: 'healthy', latency };
  } catch (error) {
    return { 
      status: 'unhealthy', 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

export default {
  supabase,
  supabaseAdmin,
  pool,
  query,
  transaction,
  testDatabaseConnection,
  closeDatabaseConnection,
  healthCheck
};

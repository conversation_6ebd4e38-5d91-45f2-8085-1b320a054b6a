import { Router, Request, Response } from 'express';
import { logger } from '../utils/logger.js';
import { redis } from '../config/redis.js';
import { healthCheck as dbHealthCheck } from '../config/database.js';
// import { githubService } from '../services/githubService.js';
// import { llmService } from '../services/ai/llmService.js';
// import { imageService } from '../services/ai/imageService.js';

const router = Router();

interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
}

interface DetailedHealthStatus extends HealthStatus {
  services: {
    database: {
      status: string;
      latency?: number;
      error?: string;
    };
    redis: {
      status: string;
      latency?: number;
      error?: string;
    };
    github: {
      status: string;
      authenticated: boolean;
      rateLimit?: any;
      error?: string;
    };
    ai: {
      llm: {
        ollama: boolean;
        huggingface: boolean;
      };
      image: {
        stableDiffusion: boolean;
        huggingface: boolean;
        unsplash: boolean;
      };
    };
  };
  system: {
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: {
      usage: number;
    };
  };
}

/**
 * @route GET /health
 * @desc Basic health check endpoint
 * @access Public
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const healthStatus: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    };

    res.status(200).json(healthStatus);
  } catch (error) {
    logger.error('Health check failed', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check service unavailable'
    });
  }
});

/**
 * @route GET /health/detailed
 * @desc Detailed health check with service status
 * @access Public
 */
router.get('/detailed', async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    // Check all services in parallel
    const [
      databaseHealth,
      redisHealth
    ] = await Promise.allSettled([
      dbHealthCheck(),
      redis.healthCheck()
    ]);

    // Get system information
    const memoryUsage = process.memoryUsage();
    const systemInfo = {
      memory: {
        used: memoryUsage.heapUsed,
        total: memoryUsage.heapTotal,
        percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100)
      },
      cpu: {
        usage: process.cpuUsage().user / 1000000 // Convert to seconds
      }
    };

    // Determine overall status
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
    
    // Check critical services
    const criticalServices = [databaseHealth, redisHealth];
    const criticalFailures = criticalServices.filter(service =>
      service.status === 'rejected' ||
      (service.status === 'fulfilled' && service.value.status === 'unhealthy')
    );

    if (criticalFailures.length > 0) {
      overallStatus = 'degraded'; // Changed to degraded since we're using in-memory storage
    }

    const detailedHealth: DetailedHealthStatus = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: databaseHealth.status === 'fulfilled'
          ? databaseHealth.value
          : { status: 'unhealthy', error: 'Service check failed' },
        redis: redisHealth.status === 'fulfilled'
          ? redisHealth.value
          : { status: 'unhealthy', error: 'Service check failed' },
        github: { status: 'disabled', authenticated: false },
        ai: {
          llm: { ollama: false, huggingface: false },
          image: { stableDiffusion: false, huggingface: false, unsplash: false }
        }
      },
      system: systemInfo
    };

    // Set appropriate HTTP status code
    const statusCode = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;

    res.status(statusCode).json(detailedHealth);

    // Log health check results
    logger.info('Detailed health check completed', {
      status: overallStatus,
      duration: Date.now() - startTime,
      services: {
        database: detailedHealth.services.database.status,
        redis: detailedHealth.services.redis.status,
        github: detailedHealth.services.github.status
      }
    });

  } catch (error) {
    logger.error('Detailed health check failed', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check service unavailable',
      duration: Date.now() - startTime
    });
  }
});

/**
 * @route GET /health/readiness
 * @desc Kubernetes readiness probe
 * @access Public
 */
router.get('/readiness', async (req: Request, res: Response) => {
  try {
    // Check critical services only
    const [databaseHealth, redisHealth] = await Promise.allSettled([
      dbHealthCheck(),
      redis.healthCheck()
    ]);

    const isReady = databaseHealth.status === 'fulfilled' && 
                   databaseHealth.value.status === 'healthy' &&
                   redisHealth.status === 'fulfilled' &&
                   redisHealth.value.status === 'healthy';

    if (isReady) {
      res.status(200).json({
        status: 'ready',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'not ready',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: 'Readiness check failed'
    });
  }
});

/**
 * @route GET /health/liveness
 * @desc Kubernetes liveness probe
 * @access Public
 */
router.get('/liveness', (req: Request, res: Response) => {
  // Simple liveness check - if the server can respond, it's alive
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

/**
 * @route GET /health/metrics
 * @desc Basic metrics endpoint
 * @access Public
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    const metrics = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      process: {
        pid: process.pid,
        version: process.version,
        platform: process.platform,
        arch: process.arch
      }
    };

    res.status(200).json(metrics);
  } catch (error) {
    logger.error('Metrics endpoint failed', error);
    res.status(500).json({
      error: 'Metrics unavailable'
    });
  }
});

export default router;

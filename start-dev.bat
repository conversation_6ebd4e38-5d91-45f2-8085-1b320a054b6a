@echo off
echo 🚀 Starting RepoSensei Development Servers...
echo.

echo 📡 Starting Backend Server...
start "RepoSensei Backend" cmd /k "cd backend && npm run dev"

echo Waiting for backend to start...
timeout /t 3 /nobreak > nul

echo.
echo 🌐 Starting Frontend Server...
start "RepoSensei Frontend" cmd /k "npm run dev"

echo.
echo ✅ Both servers are starting!
echo.
echo 🌐 Frontend will be available at: http://localhost:5173
echo 📡 Backend API will be available at: http://localhost:3001
echo.
echo Press any key to close this window...
pause > nul

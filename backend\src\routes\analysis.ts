import { Router } from 'express';
import rateLimit from 'express-rate-limit';
import { analysisController } from '../controllers/analysisController.js';
import { authMiddleware } from '../middleware/auth.js';
import { validationMiddleware } from '../middleware/validation.js';
import { logger } from '../utils/logger.js';

const router = Router();

// Rate limiting configurations
const analysisRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 analyses per 15 minutes per IP
  message: {
    success: false,
    error: 'Too many analysis requests. Please try again later.',
    retryAfter: 900
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('Analysis rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      endpoint: req.path
    });
    res.status(429).json({
      success: false,
      error: 'Too many analysis requests. Please try again later.',
      retryAfter: 900
    });
  }
});

const batchAnalysisRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 batch analyses per hour per IP
  message: {
    success: false,
    error: 'Batch analysis rate limit exceeded. Please try again later.',
    retryAfter: 3600
  }
});

const historyRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 30, // 30 requests per 5 minutes per IP
  message: {
    success: false,
    error: 'Too many history requests. Please try again later.'
  }
});

/**
 * @route POST /api/analysis/analyze
 * @desc Analyze a GitHub profile and assign anime character
 * @access Public (with rate limiting)
 */
router.post('/analyze',
  analysisRateLimit,
  // authMiddleware.optionalAuth,
  // authMiddleware.rateLimitByUser(5, 15 * 60 * 1000), // 5 per 15 min for authenticated users
  analysisController.analyzeProfile
);

/**
 * @route POST /api/analysis/batch
 * @desc Batch analyze multiple GitHub profiles
 * @access Public (with strict rate limiting)
 */
router.post('/batch',
  batchAnalysisRateLimit,
  // authMiddleware.optionalAuth,
  // authMiddleware.rateLimitByUser(2, 60 * 60 * 1000), // 2 per hour for authenticated users
  analysisController.batchAnalyze
);

/**
 * @route GET /api/analysis/history
 * @desc Get analysis history for authenticated user
 * @access Private
 */
router.get('/history',
  historyRateLimit,
  // authMiddleware.authenticate,
  analysisController.getAnalysisHistory
);

/**
 * @route GET /api/analysis/stats
 * @desc Get global analysis statistics
 * @access Public
 */
router.get('/stats',
  rateLimit({
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: 100, // 100 requests per 5 minutes per IP
    message: {
      success: false,
      error: 'Too many stats requests. Please try again later.'
    }
  }),
  analysisController.getAnalysisStats
);

export default router;

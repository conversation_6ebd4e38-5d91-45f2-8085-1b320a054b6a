import axios, { AxiosResponse } from 'axios';
import { logger, logError, logPerformance } from '../utils/logger.js';
import { redis } from '../config/redis.js';

// Types
interface GithubUser {
  login: string;
  id: number;
  avatar_url: string;
  html_url: string;
  name: string | null;
  bio: string | null;
  public_repos: number;
  followers: number;
  following: number;
  created_at: string;
  updated_at: string;
  location: string | null;
  blog: string | null;
  company: string | null;
  email: string | null;
}

interface GithubRepo {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  stargazers_count: number;
  forks_count: number;
  language: string | null;
  html_url: string;
  topics: string[];
  created_at: string;
  updated_at: string;
  pushed_at: string;
  size: number;
  open_issues_count: number;
  license: {
    key: string;
    name: string;
  } | null;
  default_branch: string;
}

interface UserProfile {
  user: GithubUser;
  repos: GithubRepo[];
  stats: {
    totalStars: number;
    totalForks: number;
    primaryLanguages: { [key: string]: number };
    recentActivity: boolean;
    accountAge: number; // in days
  };
}

interface GitHubApiError {
  message: string;
  status: number;
  documentation_url?: string;
}

class GitHubService {
  private baseUrl = 'https://api.github.com';
  private token: string | null;
  private rateLimitRemaining = 5000;
  private rateLimitReset = 0;

  constructor() {
    this.token = process.env.GITHUB_TOKEN || null;
    
    if (!this.token) {
      logger.warn('GitHub token not configured. API rate limits will be severely restricted.');
    }
  }

  /**
   * Get request headers with authentication
   */
  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Accept': 'application/vnd.github.v3+json',
      'User-Agent': 'RepoSensei/1.0'
    };

    if (this.token) {
      headers['Authorization'] = `token ${this.token}`;
    }

    return headers;
  }

  /**
   * Handle GitHub API rate limiting
   */
  private updateRateLimit(response: AxiosResponse): void {
    const remaining = response.headers['x-ratelimit-remaining'];
    const reset = response.headers['x-ratelimit-reset'];

    if (remaining) {
      this.rateLimitRemaining = parseInt(remaining, 10);
    }

    if (reset) {
      this.rateLimitReset = parseInt(reset, 10) * 1000; // Convert to milliseconds
    }

    if (this.rateLimitRemaining < 100) {
      logger.warn('GitHub API rate limit running low', {
        remaining: this.rateLimitRemaining,
        resetTime: new Date(this.rateLimitReset).toISOString()
      });
    }
  }

  /**
   * Check if we're rate limited and wait if necessary
   */
  private async checkRateLimit(): Promise<void> {
    if (this.rateLimitRemaining <= 0 && Date.now() < this.rateLimitReset) {
      const waitTime = this.rateLimitReset - Date.now();
      logger.warn('Rate limited, waiting...', { waitTime });
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  /**
   * Make authenticated request to GitHub API
   */
  private async makeRequest<T>(endpoint: string): Promise<T> {
    await this.checkRateLimit();

    try {
      const response = await axios.get<T>(`${this.baseUrl}${endpoint}`, {
        headers: this.getHeaders(),
        timeout: 10000
      });

      this.updateRateLimit(response);
      return response.data;

    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        const githubError: GitHubApiError = {
          message: error.response.data?.message || error.message,
          status: error.response.status,
          documentation_url: error.response.data?.documentation_url
        };

        // Handle specific GitHub API errors
        switch (error.response.status) {
          case 404:
            throw new Error(`GitHub user not found`);
          case 403:
            if (error.response.data?.message?.includes('rate limit')) {
              throw new Error('GitHub API rate limit exceeded. Please try again later.');
            }
            throw new Error('GitHub API access forbidden. Check your token permissions.');
          case 401:
            throw new Error('GitHub API authentication failed. Check your token.');
          default:
            throw new Error(`GitHub API error: ${githubError.message}`);
        }
      }

      throw new Error(`GitHub API request failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get cached user profile
   */
  private async getCachedProfile(username: string): Promise<UserProfile | null> {
    try {
      const cached = await redis.getJSON<UserProfile>(`github:${username}`);
      if (cached) {
        logger.info('Retrieved GitHub profile from cache', { username });
        return cached;
      }
    } catch (error) {
      logError('GitHub cache retrieval error', error as Error, { username });
    }
    return null;
  }

  /**
   * Cache user profile
   */
  private async cacheProfile(username: string, profile: UserProfile): Promise<void> {
    try {
      // Cache for 1 hour
      await redis.setJSON(`github:${username}`, profile, 3600);
      logger.info('Cached GitHub profile', { username });
    } catch (error) {
      logError('GitHub cache storage error', error as Error, { username });
    }
  }

  /**
   * Calculate user statistics
   */
  private calculateStats(user: GithubUser, repos: GithubRepo[]): UserProfile['stats'] {
    const totalStars = repos.reduce((sum, repo) => sum + repo.stargazers_count, 0);
    const totalForks = repos.reduce((sum, repo) => sum + repo.forks_count, 0);
    
    // Calculate primary languages
    const languageCounts: { [key: string]: number } = {};
    repos.forEach(repo => {
      if (repo.language) {
        languageCounts[repo.language] = (languageCounts[repo.language] || 0) + 1;
      }
    });

    // Check recent activity (pushed within last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentActivity = repos.some(repo => 
      new Date(repo.pushed_at) > thirtyDaysAgo
    );

    // Calculate account age in days
    const accountAge = Math.floor(
      (Date.now() - new Date(user.created_at).getTime()) / (1000 * 60 * 60 * 24)
    );

    return {
      totalStars,
      totalForks,
      primaryLanguages: languageCounts,
      recentActivity,
      accountAge
    };
  }

  /**
   * Get user's basic information
   */
  public async getUser(username: string): Promise<GithubUser> {
    const startTime = Date.now();
    
    try {
      const user = await this.makeRequest<GithubUser>(`/users/${username}`);
      logPerformance('GitHub User Fetch', startTime, { username });
      return user;
    } catch (error) {
      logError('Failed to fetch GitHub user', error as Error, { username });
      throw error;
    }
  }

  /**
   * Get user's repositories
   */
  public async getUserRepos(username: string, limit = 30): Promise<GithubRepo[]> {
    const startTime = Date.now();
    
    try {
      // Get repositories sorted by stars (most popular first)
      const repos = await this.makeRequest<GithubRepo[]>(
        `/users/${username}/repos?type=owner&sort=stars&direction=desc&per_page=${limit}`
      );
      
      logPerformance('GitHub Repos Fetch', startTime, { 
        username, 
        repoCount: repos.length 
      });
      
      return repos;
    } catch (error) {
      logError('Failed to fetch GitHub repositories', error as Error, { username });
      throw error;
    }
  }

  /**
   * Get comprehensive user profile with repositories and statistics
   */
  public async getUserProfile(username: string): Promise<UserProfile> {
    // Validate username
    if (!username || typeof username !== 'string' || username.trim().length === 0) {
      throw new Error('Invalid GitHub username provided');
    }

    const cleanUsername = username.trim().toLowerCase();

    // Check cache first
    const cached = await this.getCachedProfile(cleanUsername);
    if (cached) {
      return cached;
    }

    const startTime = Date.now();

    try {
      // Fetch user and repositories in parallel
      const [user, repos] = await Promise.all([
        this.getUser(cleanUsername),
        this.getUserRepos(cleanUsername, 30)
      ]);

      // Calculate statistics
      const stats = this.calculateStats(user, repos);

      const profile: UserProfile = {
        user,
        repos,
        stats
      };

      // Cache the result
      await this.cacheProfile(cleanUsername, profile);

      logPerformance('Complete GitHub Profile Fetch', startTime, {
        username: cleanUsername,
        repoCount: repos.length,
        totalStars: stats.totalStars
      });

      logger.info('GitHub profile analysis completed', {
        username: cleanUsername,
        publicRepos: user.public_repos,
        analyzedRepos: repos.length,
        totalStars: stats.totalStars,
        primaryLanguage: Object.keys(stats.primaryLanguages)[0] || 'Unknown'
      });

      return profile;

    } catch (error) {
      logError('Failed to fetch complete GitHub profile', error as Error, { 
        username: cleanUsername 
      });
      throw error;
    }
  }

  /**
   * Search for users (useful for username suggestions)
   */
  public async searchUsers(query: string, limit = 10): Promise<{ login: string; avatar_url: string }[]> {
    try {
      const response = await this.makeRequest<{ items: GithubUser[] }>(
        `/search/users?q=${encodeURIComponent(query)}&per_page=${limit}`
      );
      
      return response.items.map(user => ({
        login: user.login,
        avatar_url: user.avatar_url
      }));
    } catch (error) {
      logError('GitHub user search failed', error as Error, { query });
      return [];
    }
  }

  /**
   * Get rate limit status
   */
  public async getRateLimit(): Promise<{
    remaining: number;
    limit: number;
    reset: Date;
    used: number;
  }> {
    try {
      const response = await this.makeRequest<{
        rate: {
          limit: number;
          remaining: number;
          reset: number;
          used: number;
        };
      }>('/rate_limit');

      return {
        remaining: response.rate.remaining,
        limit: response.rate.limit,
        reset: new Date(response.rate.reset * 1000),
        used: response.rate.used
      };
    } catch (error) {
      logError('Failed to fetch rate limit', error as Error);
      throw error;
    }
  }

  /**
   * Health check for GitHub API
   */
  public async healthCheck(): Promise<{ 
    status: string; 
    authenticated: boolean; 
    rateLimit?: any; 
    error?: string 
  }> {
    try {
      const rateLimit = await this.getRateLimit();
      
      return {
        status: 'healthy',
        authenticated: !!this.token,
        rateLimit: {
          remaining: rateLimit.remaining,
          limit: rateLimit.limit,
          resetTime: rateLimit.reset.toISOString()
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        authenticated: !!this.token,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Validate if a username exists
   */
  public async validateUsername(username: string): Promise<boolean> {
    try {
      await this.getUser(username);
      return true;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const githubService = new GitHubService();
export default githubService;

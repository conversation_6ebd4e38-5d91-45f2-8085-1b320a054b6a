#!/bin/bash

echo "🚀 Starting RepoSensei Development Servers..."
echo

echo "📡 Starting Backend Server..."
cd backend
npm run dev &
BACKEND_PID=$!
cd ..

echo "Waiting for backend to start..."
sleep 3

echo
echo "🌐 Starting Frontend Server..."
npm run dev &
FRONTEND_PID=$!

echo
echo "✅ Both servers are starting!"
echo
echo "🌐 Frontend will be available at: http://localhost:5173"
echo "📡 Backend API will be available at: http://localhost:3001"
echo
echo "Press Ctrl+C to stop both servers"

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID
